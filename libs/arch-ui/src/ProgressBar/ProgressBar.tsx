import React, { createContext, type PropsWithChildren, useContext, useMemo } from 'react';
import { Progress } from 'radix-ui';
import Badge from '../Badge';
import { type BadgeProps, SHAPE, SIZE, THEME } from '../Badge/Badge.types';
import { cn } from '../utils/classes';

const progressbarColorMap = {
  linear: {
    primary: {
      default: 'bg-brand-bold',
      disabled: 'bg-brand-subtle-pressed',
    },
    success: {
      default: 'bg-success-bold',
      disabled: 'bg-success-subtle-pressed',
    },
    warning: {
      default: 'bg-warning-bold',
      disabled: 'bg-warning-subtle-pressed',
    },
    danger: {
      default: 'bg-danger-bold',
      disabled: 'bg-danger-subtle-pressed',
    },
    secondary: {
      default: 'bg-neutral-bold',
      disabled: 'bg-neutral-subtle-pressed',
    },
  },
  donut: {
    primary: {
      default: 'stroke-brand-bold',
      disabled: 'stroke-brand-subtle-pressed',
    },
    success: {
      default: 'stroke-success-bold',
      disabled: 'stroke-success-subtle-pressed',
    },
    warning: {
      default: 'stroke-warning-bold',
      disabled: 'stroke-warning-subtle-pressed',
    },
    danger: {
      default: 'stroke-danger-bold',
      disabled: 'stroke-danger-subtle-pressed',
    },
    secondary: {
      default: 'stroke-neutral-bold',
      disabled: 'stroke-neutral-subtle-pressed',
    },
  },
};

const progressbarSizeMap = {
  linear: {
    small: 'h-1',
    medium: 'h-2',
    large: 'h-3',
  },
  donut: {
    small: 'size-6',
    medium: 'size-10',
    large: 'size-12',
  },
};

const progressbarDonutConfig = {
  small: {
    size: 24,
    strokeWidth: 10,
    fontSize: 'text-xs',
    showProgress: false,
  },
  medium: {
    size: 40,
    strokeWidth: 10,
    fontSize: 'text-xs',
    showProgress: true,
  },
  large: {
    size: 64,
    strokeWidth: 10,
    fontSize: 'text-sm',
    showProgress: true,
  },
};

export type ProgressBarActiveColor = 'primary' | 'success' | 'warning' | 'danger' | 'secondary';

export type ProgressBarSize = 'small' | 'medium' | 'large';

export type ProgressBarVariant = 'linear' | 'donut';

export type ProgressBarState = 'default' | 'disabled';

export type ProgressBarProps = {
  color?: ProgressBarActiveColor;
  progress: number;
  size?: ProgressBarSize;
  disabled?: boolean;
  variant?: ProgressBarVariant;
};

type ProgressBarContextValue = {
  progress: number;
  size: ProgressBarSize;
  variant: ProgressBarVariant;
};

const ProgressBarContext = createContext<ProgressBarContextValue | null>(null);

export const useProgressBarContext = () => {
  const context = useContext(ProgressBarContext);
  if (!context) {
    throw new Error('ProgressBar child components must be used within a ProgressBar.Root component');
  }
  return context;
};

const DonutProgress = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  ({ color = 'primary', progress, size = 'medium', disabled }, ref) => {
    const config = progressbarDonutConfig[size];
    const { size: svgSize, strokeWidth, fontSize, showProgress } = config;
    const center = 50;
    const radius = 40;

    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (Math.max(0, Math.min(100, progress)) / 100) * circumference;

    const state: ProgressBarState = disabled ? 'disabled' : 'default';
    const strokeColorClass = progressbarColorMap.donut[color][state];

    return (
      <div
        ref={ref}
        className="relative inline-flex items-center justify-center"
        role="progressbar"
        aria-label={`Progress: ${Math.round(progress)}%`}
      >
        <svg width={svgSize} height={svgSize} viewBox="0 0 100 100" className="transform -rotate-90">
          <circle
            cx={center}
            cy={center}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            className="stroke-neutral-subtle"
          />
          <circle
            cx={center}
            cy={center}
            r={radius}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={strokeColorClass}
          />
        </svg>
        {showProgress && (
          <div
            className={cn(
              'absolute inset-0 flex items-center justify-center',
              fontSize,
              'font-medium leading-none',
              disabled ? 'text-neutral-subtle-pressed' : 'text-neutral-subtle'
            )}
            aria-hidden="true"
          >
            {Math.round(progress)}%
          </div>
        )}
      </div>
    );
  }
);

DonutProgress.displayName = 'DonutProgress';

export const ProgressBarRoot = React.forwardRef<
  React.ComponentRef<typeof Progress.Root | typeof DonutProgress>,
  React.ComponentPropsWithoutRef<typeof Progress.Root> & ProgressBarProps
>(
  (
    {
      color = 'primary',
      progress,
      size = 'medium',
      className,
      children,
      variant = 'linear',
      disabled,
      ...progressBarProps
    },
    ref
  ) => {
    const contextValue: ProgressBarContextValue = useMemo(
      () => ({
        progress,
        size,
        variant,
      }),
      [progress, size, variant]
    );

    if (variant === 'donut') {
      return (
        <ProgressBarContext.Provider value={contextValue}>
          <div className="flex flex-col items-center space-y-2">
            <DonutProgress color={color} progress={progress} size={size} disabled={disabled} />
            {children}
          </div>
        </ProgressBarContext.Provider>
      );
    }

    const state: ProgressBarState = disabled ? 'disabled' : 'default';
    const colorClass = progressbarColorMap.linear[color][state];

    return (
      <ProgressBarContext.Provider value={contextValue}>
        <div className="flex flex-col space-y-1">
          {children}

          <Progress.Root
            ref={ref}
            value={progress}
            className={cn('w-full rounded-full bg-neutral-subtle relative', progressbarSizeMap.linear[size], className)}
            aria-disabled={disabled}
            {...(progressBarProps as any)}
          >
            <Progress.Indicator
              aria-disabled={disabled}
              className={cn('rounded-full', progressbarSizeMap.linear[size], colorClass)}
              style={{ width: `${progress}%` }}
            />
          </Progress.Root>
        </div>
      </ProgressBarContext.Provider>
    );
  }
);
ProgressBarRoot.displayName = 'ProgressBar.Root';

export interface ProgressBarHeaderProps {
  showProgress?: boolean;
}

export const ProgressBarHeader: React.FC<PropsWithChildren<ProgressBarHeaderProps>> = ({ children, showProgress }) => {
  const { progress, size, variant } = useProgressBarContext();

  if (variant === 'donut') {
    return <div className="flex flex-col items-center space-y-1">{children}</div>;
  }

  return (
    <div className="flex justify-between space-x-2 items-baseline">
      {children && <div className="flex flex-col flex-1">{children}</div>}
      {showProgress && (
        <span
          className={cn('text-neutral-subtle leading-4 font-medium flex-shrink-0', {
            'text-[10px]': size === 'small',
            'text-xs': size === 'medium' || size === 'large',
          })}
          aria-label={`${Math.round(progress)} percent complete`}
        >
          {Math.round(progress)}%
        </span>
      )}
    </div>
  );
};
ProgressBarHeader.displayName = 'ProgressBar.Header';

export const ProgressBarTitle: React.FC<PropsWithChildren<{ className?: string }>> = ({ children, className }) => (
  <span className={cn('text-neutral text-sm leading-5 font-medium flex items-center gap-x-1', className)}>
    {children}
  </span>
);
ProgressBarTitle.displayName = 'ProgressBar.Title';

export const ProgressBarSubtitle: React.FC<PropsWithChildren<{ className?: string }>> = ({ children, className }) => (
  <span className={cn('text-neutral-subtle text-sm leading-5 font-medium flex items-center gap-x-1', className)}>
    {children}
  </span>
);
ProgressBarSubtitle.displayName = 'ProgressBar.Subtitle';

export { THEME as ProgressBarBadgeTheme };
export const ProgressBarBadge: React.FC<PropsWithChildren<Pick<BadgeProps, 'theme' | 'label'>>> = ({
  label,
  theme,
}) => <Badge label={label} size={SIZE.EXTRA_SMALL} shape={SHAPE.BASIC} theme={theme} />;
ProgressBarBadge.displayName = 'ProgressBar.Badge';
